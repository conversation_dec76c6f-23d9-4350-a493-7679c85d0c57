"use client"

import { NextIntlClientProvider } from 'next-intl';
import { useLocale } from '@/contexts/locale/LocaleContext';
import { useEffect, useState } from 'react';

interface ClientIntlProviderProps {
  children: React.ReactNode;
}

export function ClientIntlProvider({ children }: ClientIntlProviderProps) {
  const { locale } = useLocale();
  const [messages, setMessages] = useState<Record<string, unknown> | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadMessages = async () => {
      try {
        setIsLoading(true);
        const messageModule = await import(`../../../messages/${locale}.json`);
        setMessages(messageModule.default);
      } catch (error) {
        console.error(`Failed to load messages for locale ${locale}:`, error);
        // Fallback to English
        try {
          const fallbackModule = await import(`../../../messages/en.json`);
          setMessages(fallbackModule.default);
        } catch (fallbackError) {
          console.error('Failed to load fallback messages:', fallbackError);
          setMessages({});
        }
      } finally {
        setIsLoading(false);
      }
    };

    loadMessages();
  }, [locale]);

  if (isLoading || !messages) {
    return <div>{children}</div>; // Return children without i18n during loading
  }

  return (
    <NextIntlClientProvider locale={locale} messages={messages}>
      {children}
    </NextIntlClientProvider>
  );
}
