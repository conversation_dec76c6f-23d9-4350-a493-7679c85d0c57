# AWS S3 Deployment Script for Smash Music Static Export
# Usage: .\deploy-s3.ps1 -BucketName "your-bucket-name" [-AwsProfile "profile-name"]

param(
    [Parameter(Mandatory=$true)]
    [string]$BucketName,
    
    [Parameter(Mandatory=$false)]
    [string]$AwsProfile = "default"
)

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

Write-Status "Starting deployment to S3 bucket: $BucketName"
Write-Status "Using AWS profile: $AwsProfile"

# Check if AWS CLI is installed
try {
    aws --version | Out-Null
} catch {
    Write-Error "AWS CLI is not installed. Please install it first."
    exit 1
}

# Check if out directory exists
if (-not (Test-Path "out")) {
    Write-Warning "Static export not found. Building now..."
    pnpm build:static
}

# Verify the build was successful
if (-not (Test-Path "out/index.html")) {
    Write-Error "Build failed or index.html not found in out directory"
    exit 1
}

Write-Success "Static export found and verified"

# Set AWS profile if specified
if ($AwsProfile -ne "default") {
    $env:AWS_PROFILE = $AwsProfile
}

# Check if bucket exists
Write-Status "Checking if bucket exists..."
try {
    aws s3 ls "s3://$BucketName" 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Bucket $BucketName does not exist or you don't have access to it"
        exit 1
    }
} catch {
    Write-Error "Failed to check bucket existence"
    exit 1
}

Write-Success "Bucket $BucketName is accessible"

# Sync files to S3
Write-Status "Uploading files to S3..."
aws s3 sync out/ "s3://$BucketName" --delete --cache-control "public, max-age=31536000" --exclude "*.html" --exclude "*.txt"

# Upload HTML files with different cache control
Write-Status "Uploading HTML files with no-cache policy..."
aws s3 sync out/ "s3://$BucketName" --delete --cache-control "no-cache, no-store, must-revalidate" --include "*.html" --include "*.txt"

Write-Success "Files uploaded successfully!"

# Configure bucket for static website hosting
Write-Status "Configuring bucket for static website hosting..."
aws s3 website "s3://$BucketName" --index-document index.html --error-document 404.html

Write-Success "Static website hosting configured"

# Get the website URL
$Region = aws s3api get-bucket-location --bucket $BucketName --query 'LocationConstraint' --output text
if ($Region -eq "None" -or $Region -eq "null") {
    $Region = "us-east-1"
}

if ($Region -eq "us-east-1") {
    $WebsiteUrl = "http://$BucketName.s3-website-us-east-1.amazonaws.com"
} else {
    $WebsiteUrl = "http://$BucketName.s3-website-$Region.amazonaws.com"
}

Write-Success "Deployment completed!"
Write-Host ""
Write-Host "🚀 Your Smash Music app is now live at:" -ForegroundColor Cyan
Write-Host "   $WebsiteUrl" -ForegroundColor Cyan
Write-Host ""
Write-Host "📋 Next steps:" -ForegroundColor Yellow
Write-Host "   1. Configure your domain (if needed)" -ForegroundColor White
Write-Host "   2. Set up CloudFront for HTTPS and better performance" -ForegroundColor White
Write-Host "   3. Configure proper CORS for AWS Amplify authentication" -ForegroundColor White
Write-Host ""
Write-Warning "Note: Make sure your S3 bucket policy allows public read access for static website hosting"
