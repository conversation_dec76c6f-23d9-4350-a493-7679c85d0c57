#!/bin/bash

# AWS S3 Deployment Script for Smash Music Static Export
# Usage: ./deploy-s3.sh <bucket-name> [aws-profile]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if bucket name is provided
if [ -z "$1" ]; then
    print_error "Bucket name is required!"
    echo "Usage: $0 <bucket-name> [aws-profile]"
    echo "Example: $0 my-smash-music-bucket"
    echo "Example: $0 my-smash-music-bucket my-aws-profile"
    exit 1
fi

BUCKET_NAME=$1
AWS_PROFILE=${2:-default}

print_status "Starting deployment to S3 bucket: $BUCKET_NAME"
print_status "Using AWS profile: $AWS_PROFILE"

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    print_error "AWS CLI is not installed. Please install it first."
    exit 1
fi

# Check if out directory exists
if [ ! -d "out" ]; then
    print_warning "Static export not found. Building now..."
    pnpm build:static
fi

# Verify the build was successful
if [ ! -f "out/index.html" ]; then
    print_error "Build failed or index.html not found in out directory"
    exit 1
fi

print_success "Static export found and verified"

# Set AWS profile if specified
if [ "$AWS_PROFILE" != "default" ]; then
    export AWS_PROFILE=$AWS_PROFILE
fi

# Check if bucket exists
print_status "Checking if bucket exists..."
if aws s3 ls "s3://$BUCKET_NAME" 2>&1 | grep -q 'NoSuchBucket'; then
    print_error "Bucket $BUCKET_NAME does not exist or you don't have access to it"
    exit 1
fi

print_success "Bucket $BUCKET_NAME is accessible"

# Sync files to S3
print_status "Uploading files to S3..."
aws s3 sync out/ s3://$BUCKET_NAME \
    --delete \
    --cache-control "public, max-age=31536000" \
    --exclude "*.html" \
    --exclude "*.txt"

# Upload HTML files with different cache control
print_status "Uploading HTML files with no-cache policy..."
aws s3 sync out/ s3://$BUCKET_NAME \
    --delete \
    --cache-control "no-cache, no-store, must-revalidate" \
    --include "*.html" \
    --include "*.txt"

print_success "Files uploaded successfully!"

# Configure bucket for static website hosting
print_status "Configuring bucket for static website hosting..."
aws s3 website s3://$BUCKET_NAME \
    --index-document index.html \
    --error-document 404.html

print_success "Static website hosting configured"

# Get the website URL
REGION=$(aws s3api get-bucket-location --bucket $BUCKET_NAME --query 'LocationConstraint' --output text)
if [ "$REGION" = "None" ]; then
    REGION="us-east-1"
fi

if [ "$REGION" = "us-east-1" ]; then
    WEBSITE_URL="http://$BUCKET_NAME.s3-website-us-east-1.amazonaws.com"
else
    WEBSITE_URL="http://$BUCKET_NAME.s3-website-$REGION.amazonaws.com"
fi

print_success "Deployment completed!"
echo ""
echo "🚀 Your Smash Music app is now live at:"
echo "   $WEBSITE_URL"
echo ""
echo "📋 Next steps:"
echo "   1. Configure your domain (if needed)"
echo "   2. Set up CloudFront for HTTPS and better performance"
echo "   3. Configure proper CORS for AWS Amplify authentication"
echo ""
print_warning "Note: Make sure your S3 bucket policy allows public read access for static website hosting"
