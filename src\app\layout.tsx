import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/contexts/theme/ThemeContext";
import { AuthProvider } from "@/contexts/auth/AuthContext";
import { LocaleProvider } from "@/contexts/locale/LocaleContext";
import { MusicPlayerProvider } from "@/contexts/music-player-context/music-player-context"
import Header from "@/components/shared/Header";
import { ClientIntlProvider } from "@/components/providers/ClientIntlProvider";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Smash Music",
  description: "A modern music application with dark/light theme support",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <LocaleProvider>
          <ClientIntlProvider>
            <ThemeProvider>
              <AuthProvider>
                <MusicPlayerProvider>
                  <Header />
                  {children}
                </MusicPlayerProvider>
              </AuthProvider>
            </ThemeProvider>
          </ClientIntlProvider>
        </LocaleProvider>
      </body>
    </html>
  );
}
