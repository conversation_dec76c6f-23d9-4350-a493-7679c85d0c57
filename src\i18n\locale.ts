// Client-side locale management for static export
import {Locale, defaultLocale} from '@/i18n/config';

const LOCALE_STORAGE_KEY = 'NEXT_LOCALE';

export function getUserLocale(): Locale {
  if (typeof window === 'undefined') {
    return defaultLocale;
  }

  const savedLocale = localStorage.getItem(LOCALE_STORAGE_KEY) as Locale;
  return savedLocale && ['en', 'es', 'fr'].includes(savedLocale) ? savedLocale : defaultLocale;
}

export function setUserLocale(locale: Locale): void {
  if (typeof window !== 'undefined') {
    localStorage.setItem(LOCALE_STORAGE_KEY, locale);
  }
}