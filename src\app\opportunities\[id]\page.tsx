"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  ArrowLeft,
  MapPin,
  Calendar,
  DollarSign,
  Users,
  MessageCircle,
  Send,
  Music,
  Clock,
  CheckCircle,
} from "lucide-react"
import Link from "next/link"
import type { Opportunity } from "../../../features/opportunites/types"

// Mock data for the opportunity
const mockOpportunity: Opportunity = {
  id: "1",
  title: "Session Guitarist for Indie Rock EP",
  role: "Guitarist",
  description: `We're looking for a talented session guitarist to join our indie rock project "Night Drive EP". This is a 4-track EP with influences from Arctic Monkeys, The Strokes, and Tame Impala.

The project requires clean, melodic guitar work with some ambient textures. We need someone who can:
- Record high-quality guitar tracks remotely
- Provide creative input on arrangements
- Work collaboratively with our team
- Meet deadlines and communicate effectively

This is a paid opportunity with potential for ongoing collaboration. We're looking for someone passionate about indie rock who can bring fresh ideas to our sound.`,
  postedBy: {
    id: "2",
    name: "The Midnight Band",
    type: "band",
    avatar: "/placeholder.svg?height=60&width=60",
  },
  genre: ["Indie", "Alternative", "Rock"],
  timeline: {
    startDate: "2024-07-01",
    endDate: "2024-07-31",
  },
  location: {
    type: "remote",
  },
  payment: {
    type: "paid",
    amount: 5000,
    currency: "₹",
  },
  associatedProject: {
    id: "album-2",
    name: "Night Drive EP",
    type: "album",
  },
  requirements: [
    "Professional recording setup",
    "Experience with indie rock",
    "Own electric guitar and amp",
    "Audio interface and DAW",
    "Portfolio of previous work",
  ],
  status: "open",
  applicationsCount: 8,
  createdAt: "2024-01-10",
  updatedAt: "2024-01-10",
}

// Mock similar opportunities
const similarOpportunities = [
  {
    id: "2",
    title: "Bassist for Rock Band",
    postedBy: "Rock Collective",
    genre: ["Rock", "Alternative"],
    payment: "₹3,000",
  },
  {
    id: "3",
    title: "Drummer for Recording Session",
    postedBy: "Studio Sessions",
    genre: ["Indie", "Pop"],
    payment: "₹4,500",
  },
]

export default function OpportunityDetailPage() {
  const [applicationMessage, setApplicationMessage] = useState("")
  const [isApplying, setIsApplying] = useState(false)
  const [hasApplied, setHasApplied] = useState(false)

  const handleApply = async () => {
    setIsApplying(true)

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000))

    setHasApplied(true)
    setIsApplying(false)
    setApplicationMessage("")
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="px-4 md:px-8 lg:px-16 py-8">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Link href="/opportunities">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Opportunities
            </Button>
          </Link>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Opportunity Header */}
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-start gap-4 mb-6">
                  <Avatar className="w-16 h-16">
                    <AvatarImage src={mockOpportunity.postedBy.avatar || "/placeholder.svg"} />
                    <AvatarFallback>
                      {mockOpportunity.postedBy.name
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <h1 className="text-3xl font-bold mb-2">{mockOpportunity.title}</h1>
                    <p className="text-muted-foreground mb-3">Posted by {mockOpportunity.postedBy.name}</p>
                    <div className="flex flex-wrap gap-2">
                      {mockOpportunity.genre.map((genre) => (
                        <Badge key={genre} variant="secondary">
                          {genre}
                        </Badge>
                      ))}
                      <Badge variant="outline" className="text-green-600 border-green-600">
                        {mockOpportunity.status === "open" ? "Open" : "Closed"}
                      </Badge>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                  <div className="flex items-center gap-2 text-sm">
                    <MapPin className="w-4 h-4 text-muted-foreground" />
                    <span className="capitalize">{mockOpportunity.location.type}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <DollarSign className="w-4 h-4 text-muted-foreground" />
                    <span>
                      {mockOpportunity.payment.type === "paid"
                        ? `${mockOpportunity.payment.currency}${mockOpportunity.payment.amount?.toLocaleString()}`
                        : mockOpportunity.payment.type}
                    </span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <Calendar className="w-4 h-4 text-muted-foreground" />
                    <span>{new Date(mockOpportunity.timeline.startDate).toLocaleDateString()}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <Users className="w-4 h-4 text-muted-foreground" />
                    <span>{mockOpportunity.applicationsCount} applicants</span>
                  </div>
                </div>

                {mockOpportunity.associatedProject && (
                  <div className="p-4 bg-muted/50 rounded-lg mb-6">
                    <div className="flex items-center gap-2">
                      <Music className="w-5 h-5 text-primary" />
                      <span className="font-medium">Associated Project:</span>
                      <span className="text-primary cursor-pointer hover:underline">
                        {mockOpportunity.associatedProject.name}
                      </span>
                    </div>
                  </div>
                )}

                <div className="flex gap-3">
                  {hasApplied ? (
                    <Button disabled className="gap-2">
                      <CheckCircle className="w-4 h-4" />
                      Application Sent
                    </Button>
                  ) : (
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button className="gap-2">
                          <Send className="w-4 h-4" />
                          Apply Now
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Apply for this Opportunity</DialogTitle>
                          <DialogDescription>
                            Send a message to the poster explaining why you are a good fit for this role.
                          </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4">
                          <Textarea
                            placeholder="Tell them about your experience, why you're interested, and what you can bring to the project..."
                            value={applicationMessage}
                            onChange={(e) => setApplicationMessage(e.target.value)}
                            className="min-h-[120px]"
                          />
                          <div className="flex gap-2">
                            <Button
                              onClick={handleApply}
                              disabled={isApplying || !applicationMessage.trim()}
                              className="flex-1"
                            >
                              {isApplying ? "Sending..." : "Send Application"}
                            </Button>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                  )}

                  <Button variant="outline" className="gap-2">
                    <MessageCircle className="w-4 h-4" />
                    Message Poster
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Description */}
            <Card>
              <CardHeader>
                <CardTitle>Description</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="prose prose-sm max-w-none">
                  {mockOpportunity.description.split("\n\n").map((paragraph, index) => (
                    <p key={index} className="mb-4 last:mb-0">
                      {paragraph}
                    </p>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Requirements */}
            <Card>
              <CardHeader>
                <CardTitle>Requirements</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {mockOpportunity.requirements.map((requirement, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                      <span>{requirement}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Timeline */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="w-5 h-5" />
                  Timeline
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="text-sm font-medium">Start Date</p>
                  <p className="text-muted-foreground">
                    {new Date(mockOpportunity.timeline.startDate).toLocaleDateString("en-US", {
                      weekday: "long",
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    })}
                  </p>
                </div>
                <Separator />
                <div>
                  <p className="text-sm font-medium">End Date</p>
                  <p className="text-muted-foreground">
                    {new Date(mockOpportunity.timeline.endDate).toLocaleDateString("en-US", {
                      weekday: "long",
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    })}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Similar Opportunities */}
            <Card>
              <CardHeader>
                <CardTitle>Similar Opportunities</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {similarOpportunities.map((opportunity) => (
                  <div key={opportunity.id} className="space-y-2">
                    <h4 className="font-medium text-sm text-primary cursor-pointer hover:underline">
                      {opportunity.title}
                    </h4>
                    <p className="text-xs text-muted-foreground">by {opportunity.postedBy}</p>
                    <div className="flex items-center justify-between">
                      <div className="flex gap-1">
                        {opportunity.genre.map((genre) => (
                          <Badge key={genre} variant="outline" className="text-xs">
                            {genre}
                          </Badge>
                        ))}
                      </div>
                      <span className="text-xs font-medium">{opportunity.payment}</span>
                    </div>
                    <Separator />
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* More by Artist */}
            <Card>
              <CardHeader>
                <CardTitle>More by {mockOpportunity.postedBy.name}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">No other active opportunities from this poster.</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
